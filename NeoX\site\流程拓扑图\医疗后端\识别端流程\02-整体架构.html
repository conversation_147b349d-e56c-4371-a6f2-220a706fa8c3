
<!DOCTYPE html>

<html class="no-js" lang="zh">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width,initial-scale=1" name="viewport"/>
<meta content="Docs site for NeoX." name="description"/>
<meta content="SongLin Lu" name="author"/>
<link href="01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" rel="prev"/>
<link href="03-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" rel="next"/>
<link href="../../../assets/images/favicon.png" rel="icon"/>
<meta content="mkdocs-1.6.1, mkdocs-material-9.6.18" name="generator"/>
<title>02-整体架构 - NeoX Docs</title>
<link href="../../../assets/stylesheets/main.7e37652d.min.css" rel="stylesheet"/>
<link href="../../../assets/stylesheets/palette.06af60db.min.css" rel="stylesheet"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&amp;display=fallback" rel="stylesheet"/>
<style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
<script>__md_scope=new URL("../../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
</head>
<body data-md-color-accent="blue" data-md-color-primary="blue" data-md-color-scheme="default" dir="ltr">
<input autocomplete="off" class="md-toggle" data-md-toggle="drawer" id="__drawer" type="checkbox"/>
<input autocomplete="off" class="md-toggle" data-md-toggle="search" id="__search" type="checkbox"/>
<label class="md-overlay" for="__drawer"></label>
<div data-md-component="skip">
</div>
<div data-md-component="announce">
</div>
<header class="md-header" data-md-component="header">
<nav aria-label="页眉" class="md-header__inner md-grid">
<a aria-label="NeoX Docs" class="md-header__button md-logo" data-md-component="logo" href="../../../index.html" title="NeoX Docs">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
<label class="md-header__button md-icon" for="__drawer">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"></path></svg>
</label>
<div class="md-header__title" data-md-component="header-title">
<div class="md-header__ellipsis">
<div class="md-header__topic">
<span class="md-ellipsis">
            NeoX Docs
          </span>
</div>
<div class="md-header__topic" data-md-component="header-topic">
<span class="md-ellipsis">
            
              02-整体架构
            
          </span>
</div>
</div>
</div>
<form class="md-header__option" data-md-component="palette">
<input aria-hidden="true" class="md-option" data-md-color-accent="blue" data-md-color-media="" data-md-color-primary="blue" data-md-color-scheme="default" id="__palette_0" name="__palette" type="radio"/>
</form>
<script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
</nav>
</header>
<div class="md-container" data-md-component="container">
<nav aria-label="标签" class="md-tabs" data-md-component="tabs">
<div class="md-grid">
<ul class="md-tabs__list">
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../../index.html">
        
  
  
    
  
  主页

      </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../../SOP/%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
          
  
  
  SOP

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html">
          
  
  
  开发环境搭建

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html">
          
  
  
  自动化运维

        </a>
</li>
<li class="md-tabs__item md-tabs__item--active">
<a class="md-tabs__link" href="01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
          
  
  
  流程拓扑图

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html">
          
  
  
  关于

        </a>
</li>
</ul>
</div>
</nav>
<main class="md-main" data-md-component="main">
<div class="md-main__inner md-grid">
<div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation">
<div class="md-sidebar__scrollwrap">
<div class="md-sidebar__inner">
<nav aria-label="导航栏" class="md-nav md-nav--primary md-nav--lifted md-nav--integrated" data-md-level="0">
<label class="md-nav__title" for="__drawer">
<a aria-label="NeoX Docs" class="md-nav__button md-logo" data-md-component="logo" href="../../../index.html" title="NeoX Docs">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
    NeoX Docs
  </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../../index.html">
<span class="md-ellipsis">
    主页
    
  </span>
</a>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
<span class="md-ellipsis">
    SOP
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_2_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_2">
<span class="md-nav__icon md-icon"></span>
            SOP
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../../SOP/%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    项目研发管理流程规范
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../../SOP/%E6%B5%8B%E8%AF%95%E5%8F%8A%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    测试及发布流程规范
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../../SOP/%E4%BA%A7%E5%93%81%E7%89%88%E6%9C%AC%E5%91%BD%E5%90%8D%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    产品版本命名规范
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../../SOP/%E7%94%9F%E4%BA%A7%E6%95%85%E9%9A%9C%E5%AF%B9%E5%BA%94%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    生产故障对应流程规范
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_3" type="checkbox"/>
<label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
<span class="md-ellipsis">
    开发环境搭建
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_3_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_3">
<span class="md-nav__icon md-icon"></span>
            开发环境搭建
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_3_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_3_1" id="__nav_3_1_label" tabindex="0">
<span class="md-ellipsis">
    后端开发
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_3_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_3_1">
<span class="md-nav__icon md-icon"></span>
            后端开发
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html">
<span class="md-ellipsis">
    代码部署
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/AWS-ECR%E6%9D%83%E9%99%90%E8%AE%BE%E7%BD%AE.html">
<span class="md-ellipsis">
    AWS ECR权限设置
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/Docker%E7%8E%AF%E5%A2%83%E9%83%A8%E7%BD%B2.html">
<span class="md-ellipsis">
    Docker环境部署
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E5%90%8E%E7%AB%AF%E4%BB%A3%E7%A0%81%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE.html">
<span class="md-ellipsis">
    后端代码环境配置
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/FAQ.html">
<span class="md-ellipsis">
    常见问题解答
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_4" type="checkbox"/>
<label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
<span class="md-ellipsis">
    自动化运维
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_4_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_4">
<span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_4_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_4_1" id="__nav_4_1_label" tabindex="0">
<span class="md-ellipsis">
    自动化发布平台
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_4_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_4_1">
<span class="md-nav__icon md-icon"></span>
            自动化发布平台
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html">
<span class="md-ellipsis">
    Ansible Semaphore
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
<input checked="" class="md-nav__toggle md-toggle" id="__nav_5" type="checkbox"/>
<label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="">
<span class="md-ellipsis">
    流程拓扑图
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="true" aria-labelledby="__nav_5_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_5">
<span class="md-nav__icon md-icon"></span>
            流程拓扑图
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
<input checked="" class="md-nav__toggle md-toggle" id="__nav_5_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_1" id="__nav_5_1_label" tabindex="">
<span class="md-ellipsis">
    医疗后端
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="true" aria-labelledby="__nav_5_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_1">
<span class="md-nav__icon md-icon"></span>
            医疗后端
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--active md-nav__item--nested">
<input checked="" class="md-nav__toggle md-toggle" id="__nav_5_1_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_1_1" id="__nav_5_1_1_label" tabindex="0">
<span class="md-ellipsis">
    识别端流程
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="true" aria-labelledby="__nav_5_1_1_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_1_1">
<span class="md-nav__icon md-icon"></span>
            识别端流程
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
<span class="md-ellipsis">
    01-生命周期
    
  </span>
</a>
</li>
<li class="md-nav__item md-nav__item--active">
<input class="md-nav__toggle md-toggle" id="__toc" type="checkbox"/>
<a class="md-nav__link md-nav__link--active" href="02-%E6%95%B4%E4%BD%93%E6%9E%B6%E6%9E%84.html">
<span class="md-ellipsis">
    02-整体架构
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="03-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
<span class="md-ellipsis">
    03-识别任务的生命周期
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="04-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%8A%B6%E6%80%81%E6%B5%81%E8%BD%AC.html">
<span class="md-ellipsis">
    04-识别任务状态流转
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="05-%E7%94%A8%E4%BA%8E%E7%BB%93%E6%9E%9C%E8%BD%AE%E8%AF%A2%E7%9A%84Redis%E7%BC%93%E5%AD%98.html">
<span class="md-ellipsis">
    05-用于结果轮询的Redis缓存
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="06-%E6%9C%8D%E5%8A%A1%E5%88%86%E5%B1%82%E6%9E%B6%E6%9E%84-%E7%BB%84%E4%BB%B6.html">
<span class="md-ellipsis">
    06-服务分层架构-组件
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="07-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1task%E4%BF%A1%E6%81%AF%E8%AF%A6%E8%BF%B0.html">
<span class="md-ellipsis">
    07-识别任务task信息详述
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="08-%E5%A4%84%E6%96%B9%E5%90%88%E5%B9%B6%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    08-处方合并流程
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="09-FAX%E5%8F%97%E4%BB%98%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    09-FAX受付流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%A4%84%E6%96%B9%E8%B0%83%E5%BA%A6%E5%99%A8%E6%9C%BA%E5%88%B6.html">
<span class="md-ellipsis">
    处方调度器机制
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../NSIPS.html">
<span class="md-ellipsis">
    NSIPS
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../GPU%E5%BC%95%E6%93%8E%E8%AF%86%E5%88%AB%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    GPU 引擎识别流程
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../Smart-Merge.html">
<span class="md-ellipsis">
    Smart Merge
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--section md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_2" id="__nav_5_2_label" tabindex="">
<span class="md-ellipsis">
    薬師丸賢太
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_2_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_2">
<span class="md-nav__icon md-icon"></span>
            薬師丸賢太
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E8%96%AC%E5%B8%AB%E4%B8%B8%E8%B3%A2%E5%A4%AA/%E5%A4%84%E6%96%B9%E7%AC%BA%E4%BF%9D%E5%AD%98%E5%8C%B9%E9%85%8D%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    处方笺保存匹配流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--section md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3" id="__nav_5_3_label" tabindex="">
<span class="md-ellipsis">
    スマート薬局
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_3">
<span class="md-nav__icon md-icon"></span>
            スマート薬局
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3_1" id="__nav_5_3_1_label" tabindex="0">
<span class="md-ellipsis">
    薬師丸撫子
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_1_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_3_1">
<span class="md-nav__icon md-icon"></span>
            薬師丸撫子
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E8%96%AC%E5%B8%AB%E4%B8%B8%E6%92%AB%E5%AD%90/%E6%89%AB%E6%8F%8F%E4%BB%AA%E8%BF%9E%E6%8E%A5%E7%BB%93%E6%9E%9C%E8%8E%B7%E5%8F%96%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    扫描仪连接结果获取流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3_2" id="__nav_5_3_2_label" tabindex="0">
<span class="md-ellipsis">
    通用模块
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_2_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_3_2">
<span class="md-nav__icon md-icon"></span>
            通用模块
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E9%80%9A%E7%94%A8%E6%A8%A1%E5%9D%97/%E6%97%A5%E5%BF%97%E4%B8%8A%E4%BC%A0%E5%AE%A2%E6%88%B7%E7%AB%AF%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    日志上传客户端流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--section md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_4" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_4" id="__nav_5_4_label" tabindex="">
<span class="md-ellipsis">
    自动化运维
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_4_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_4">
<span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/medical-backend.html">
<span class="md-ellipsis">
    medical-backend
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/performance.html">
<span class="md-ellipsis">
    performance
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/terraform.html">
<span class="md-ellipsis">
    terraform
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_6" type="checkbox"/>
<label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
<span class="md-ellipsis">
    关于
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_6_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_6">
<span class="md-nav__icon md-icon"></span>
            关于
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html">
<span class="md-ellipsis">
    版本说明
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</div>
</div>
</div>
<div class="md-content" data-md-component="content">
<article class="md-content__inner md-typeset">
<h1>02-整体架构</h1>
<div class="mermaid">flowchart TD
    subgraph "Client 客户端层"
        A1[发起apply请求]
        A2[上传图片文件]
        A3[轮询qrListIds]
        A4[调用qrNoticeDownloaded]
    end

    subgraph "API Controller 控制器层"
        B1[PrescriptionAsyncController::apply]
        B2[PrescriptionAsyncController::uploadFile]
        B3[PrescriptionAsyncController::qrListIds]
        B4[PrescriptionAsyncController::qrNoticeDownloaded]
    end

    subgraph "Task Management 任务管理层"
        C1[PTask::create&lt;br/&gt;创建任务记录]
        C2[更新任务状态&lt;br/&gt;WAIT_UPLOAD → WAIT_RECOGNIZE]
        C3[任务调度&lt;br/&gt;PrescriptionAsyncDispatcher]
        C4[临时表&lt;br/&gt;p_task_temp]
    end

    subgraph "Recognition 识别处理层"
        D1[PrescriptionAsyncRecognize&lt;br/&gt;识别进程]
        D2[PrescriptionTrait::identify&lt;br/&gt;OCR/QR识别]
        D3[Selector引擎&lt;br/&gt;文字识别]
        D4[JahisParser&lt;br/&gt;QR码解析]
    end

    subgraph "Merge &amp; Finish 合并完成层"
        E1[PrescriptionAsyncMerge&lt;br/&gt;合并进程]
        E2[mergeTaskList&lt;br/&gt;处方合并逻辑]
        E3[finishPrescription&lt;br/&gt;处方完成]
        E4[PrescriptionOriginal::store&lt;br/&gt;处方入库]
    end

    subgraph "Redis Cache 缓存层"
        F1[任务状态缓存&lt;br/&gt;QR_LIST_STATUS_*]
        F2[识别结果缓存&lt;br/&gt;PrescriptionAsyncRecognizedMsg]
        F3[轮询时间戳&lt;br/&gt;FetchStatusTimeStamp]
    end

    subgraph "Database 数据库层"
        G1[(p_task&lt;br/&gt;处方识别任务表)]
        G2[(p_task_failed&lt;br/&gt;失败任务表)]
        G3[(prescription_original&lt;br/&gt;处方结果表)]
        G4[(p_task_temp&lt;br/&gt;临时任务表)]
    end

    subgraph "QPS &amp; Process 资源管理层"
        H1[QPS锁管理&lt;br/&gt;控制识别频率]
        H2[ProcessPool&lt;br/&gt;进程池管理]
    end

    %% 客户端到控制器层连接
    A1 --&gt; B1
    A2 --&gt; B2
    A3 --&gt; B3
    A4 --&gt; B4

    %% 控制器层到任务管理层连接
    B1 --&gt; C1
    B2 --&gt; C2
    B3 --&gt; F1
    B3 --&gt; F2
    B3 --&gt; F3
    B4 --&gt; F2

    %% 任务管理层连接
    C1 --&gt; G1
    C2 --&gt; G1
    C3 --&gt; H1
    C3 --&gt; H2
    C3 --&gt; C4
    C4 --&gt; G4
    C4 --&gt; D1

    %% 识别处理层连接
    D1 --&gt; D2
    D2 --&gt; D3
    D2 --&gt; D4
    D1 --&gt; G1
    D1 --&gt; G2

    %% 合并完成层连接
    G1 --&gt; E1
    E1 --&gt; E2
    E1 --&gt; G2
    E2 --&gt; E3
    E3 --&gt; E4
    E4 --&gt; G3
    E3 --&gt; F1
    E3 --&gt; F2

    %% 样式类定义
    classDef client fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef controller fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef taskMgmt fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef recognition fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef merge fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef cache fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef database fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef failedDb fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef resource fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#33691e

    %% 应用样式类
    class A1,A2,A3,A4 client
    class B1,B2,B3,B4 controller
    class C1,C2,C3,C4 taskMgmt
    class D1,D2,D3,D4 recognition
    class E1,E2,E3,E4 merge
    class F1,F2,F3 cache
    class G1,G3,G4 database
    class G2 failedDb
    class H1,H2 resource
</div>
</article>
</div>
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
</div>
<button class="md-top md-icon" data-md-component="top" hidden="" type="button">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"></path></svg>
  回到页面顶部
</button>
</main>
<footer class="md-footer">
<div class="md-footer-meta md-typeset">
<div class="md-footer-meta__inner md-grid">
<div class="md-copyright">
<div class="md-copyright__highlight">
      Copyright © 2025 SongLin Lu. All rights reserved.
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" rel="noopener" target="_blank">
      Material for MkDocs
    </a>
</div>
</div>
</div>
</footer>
</div>
<div class="md-dialog" data-md-component="dialog">
<div class="md-dialog__inner md-typeset"></div>
</div>
<script id="__config" type="application/json">{"base": "../../..", "features": ["navigation.sections", "navigation.expand", "navigation.tabs", "navigation.top", "navigation.tracking", "toc.follow", "toc.integrate"], "search": "../../../assets/javascripts/workers/search.973d3a69.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": null}</script>
<script src="../../../assets/javascripts/bundle.92b07e13.min.js"></script>
<script type="module">import mermaid from "https://unpkg.com/mermaid@10.4.0/dist/mermaid.esm.min.mjs";
mermaid.initialize({});</script></body>
</html>
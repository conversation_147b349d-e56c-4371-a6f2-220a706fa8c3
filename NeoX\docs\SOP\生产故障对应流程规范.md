# 生产故障对应流程规范

## 文档信息

- **文档标题**：生产故障反馈后开发流程规范SOP
- **版本**：1.0
- **制定日期**：2025-09-16
- **制定人**：路宋麟
- **评审人**：何书勉、胡心怡
- **适用范围**：适用于商务团队反馈线上客户问题后的技术开发响应流程，涉及技术团队、工程师、QA等角色。
- **目的**：标准化生产故障反馈处理流程，确保问题高效排查、记录和解决，提升系统稳定性和响应速度。
- **相关工具**：Jira（用于故障票管理）。

## 责任分工

- **商务团队**：负责反馈线上客户提出的问题。
- **技术团队对接人**：确认问题、评估需求、创建Jira票、委派工程师。
- **工程师（前端/后端等）**：排查问题、记录故障细节、提供解决方案。
- **QA团队**：验证解决方案的有效性。
- **发布团队**：负责按计划发布上线。

## 流程步骤

### 流程图

```mermaid
flowchart TD
    A[商务团队反馈线上客户问题] --> B{技术对接人确认是否需要开发排查?}
    B -->|不需要| C[直接回复商务告知解决方法]
    B -->|需要| D[评估需要哪端开发配合<br>委派对应工程师排查]
    D --> E[在Jira创建故障管理票<br>委派给工程师<br>记录排查流程]
    E --> F[Jira票记录: 故障现象、原因、责任人、解决方案、预计修复时间]
    F --> G[工程师对应完毕<br>QA确认及验证]
    G --> H[按计划发布上线<br>关闭Jira票]
    C --> I[流程结束]
    H --> I[流程结束]
```



### 步骤说明

1. **问题反馈确认**： 当商务与技术团队反馈了线上客户提出的问题后，技术团队对接人确认是否需要开发排查对应。如果不需要，则对接人直接回复商务告知解决方法；如果需要，则进入下一步。

2. **评估与委派**： 对接人负责评估需要哪一端的开发配合（例如前端/后端），并委派对应端的工程师排查问题。同时，在Jira中创建对应的故障管理票，将票委派给对应的工程师，并记录整个排查流程。

3. **Jira票记录**： 

   对应故障的Jira票中需要记录的内容包括：

   1. 故障现象（详细描述问题表现）
   2. 故障原因（根因分析）
   3. 故障责任人（相关负责人）
   4. 解决方案（修复方案）
   5. 预计修复并发布上线的时间（时间节点）

4. **验证与关闭**： 开发对应完毕后，经QA确认及验证，按计划发布上线后关闭对应故障的Jira票。

## 注意事项

- 所有步骤需及时记录，确保Jira票实时更新。
- 如果问题涉及多个端点，需在Jira票中明确分工。
- 紧急故障需优先处理，并通知相关方。
- 定期回顾SOP，确保流程优化。
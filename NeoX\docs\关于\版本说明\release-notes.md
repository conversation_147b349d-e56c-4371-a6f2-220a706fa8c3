# 版本说明

本文档记录了 NeoX 文档系统的版本更新历史和重要变更。

## v2025091601（2025年9月16日 第1版）

### 📋 SOP文档新增

- **生产故障对应流程规范**

  - 新增生产故障反馈后开发流程规范SOP文档

  - 标准化故障处理流程，涉及商务团队、技术团队、QA团队等多个角色

  - 包含完整的责任分工、流程步骤和注意事项

### 🎨 Mermaid 流程图标准化优化

- **生产故障流程图现代化**

  - 对生产故障对应流程规范.md进行完整的现代化语法优化

  - 使用现代 `flowchart TD` 语法和标准样式类定义

  - 采用语义化英文节点命名（Start, Confirm, Evaluate等）

### 📝 逻辑分组和样式优化

- **流程结构重组**

  - 使用 `subgraph` 将流程分为3个逻辑组：问题反馈阶段、评估处理阶段、执行验证阶段

  - 应用5种标准样式类，提供清晰的视觉层次区分

  - 采用模板标准颜色方案，确保视觉一致性

### 🔧 文档导航更新

- **新增SOP章节**

  - 在mkdocs.yml导航配置中新增生产故障对应流程规范条目

  - 在index.md主页SOP部分添加详细的文档介绍和链接

  - 完善SOP文档的覆盖范围和可访问性

## v2025090201（2025年9月2日 第1版）

### 🎨 Mermaid 流程图标准化优化

- **处方调度器机制流程图现代化**

  - 对处方调度器机制.md进行完整的现代化语法优化

  - 将旧语法 `graph TD` 更新为现代 `flowchart TD` 语法

  - 使用语义化英文节点命名，提高代码可维护性

### 📝 逻辑分组和样式优化

- **流程结构重组**

  - 使用 `subgraph` 将流程分为4个逻辑组：初始化阶段、数据获取阶段、任务调度阶段、孤儿任务处理阶段

  - 应用6种标准样式类，提供清晰的视觉层次区分

  - 采用模板标准颜色方案，确保视觉一致性

### 🔧 语法兼容性修复

- **特殊字符处理**

  - 对所有可能引起Mermaid解析冲突的特殊字符进行HTML实体编码

  - 修复花括号 `{}` → `&#123;&#125;`、圆括号 `()` → `&#40;&#41;`、方括号 `[]` → `&#91;&#93;` 的语法冲突

  - 确保流程图能够在所有支持Mermaid的平台上正常渲染

### ✅ 质量保证

- **流程完整性维护**

  - 保持所有原有业务逻辑和流程连接关系不变

  - 维护所有中文描述文本的完整性和准确性

  - 确保处方调度器核心机制的技术准确性

### 📚 文档导航更新

- **新增处方调度器章节**

  - 在index.md医疗后端流程表格中新增处方调度器机制条目

  - 提供详细的流程描述和快速访问链接

  - 完善医疗后端流程文档的覆盖范围

---

**发布日期**：2025年9月2日

**版本类型**：流程图标准化优化版本

**维护团队**：NeoX 开发团队

## v2025072901（2025年7月29日 第1版）

### 🚀 新功能

- **SOP文档体系建立**

  - 新增项目研发管理流程规范文档，规范化项目开发管理标准流程

  - 新增测试及发布流程规范文档，标准化测试和发布操作程序

  - 新增产品版本命名规范文档，建立统一的版本控制和命名标准

### 📝 导航结构优化

- **SOP章节添加**

  - 在文档导航中新增SOP（标准操作程序）专门章节

  - 提供3个核心SOP文档的快速访问链接

  - 改善文档的可发现性和组织结构

### 🔧 文档结构完善

- **标准化流程建立**

  - 建立完整的标准操作程序文档体系

  - 涵盖项目管理、测试、发布和版本控制等关键环节

  - 为NeoX项目提供统一的工作流程规范

### ✅ 质量保证

- **流程标准化**

  - 确保所有团队成员遵循统一的操作标准

  - 提高项目管理和开发流程的一致性

  - 增强团队协作效率和项目交付质量

---

**发布日期**：2025年7月29日

**版本类型**：功能增强版本

**维护团队**：NeoX 开发团队

## v2025072802（2025年7月28日 第2版）

### 🔄 处方合并流程优化

- **合并策略重构**

  - 将即时合并模式改为批量合并模式，提高处理效率

  - 多个"调用 mergeTaskToQR"和"调用 mergeTaskToOCR"步骤统一改为"记录合并的taskId"

  - 最终通过"调用combineTaskData批量合并taskIds"进行统一批量处理

### ⚡ 性能优化改进

- **批量处理机制**

  - 减少频繁的合并调用，降低系统资源消耗

  - 优化大批量处方处理的性能表现

  - 提升整体处理效率和系统稳定性

### 📝 流程文档更新

- **处方合并流程图更新**

  - 更新08-处方合并流程.md文档，反映最新的批量合并实现逻辑

  - 保持流程图与实际代码实现的一致性

  - 确保文档的准确性和实用性

---

**发布日期**：2025年7月28日

**版本类型**：性能优化版本

**维护团队**：NeoX 开发团队

## v2025072801（2025年7月28日 第1版）

### 🎨 流程图标准化优化

- **NSIPS 匹配服务流程图更新**

  - 对处方笺保存匹配流程.md进行样式类标准化优化

  - 更新样式类定义，采用模板标准颜色方案，提高视觉一致性

  - 重新分配样式类应用，根据实际节点功能进行语义化分类

  - 优化流程图的视觉层次和可读性

### 📝 流程优化改进

- **智能药局扫描仪流程优化**

  - 移除QR预处理步骤，简化扫描仪连接结果获取流程

  - 优化处理逻辑结构，提高流程执行效率

  - 减少不必要的处理环节，提升用户体验

### ✅ 质量保证

- **文档一致性维护**

  - 保持所有流程逻辑和业务功能的完整性

  - 确保样式标准化不影响原有流程的准确性

  - 维护文档的专业性和技术准确性

---

**发布日期**：2025年7月28日

**版本类型**：流程优化版本

**维护团队**：NeoX 开发团队

## v2025072301（2025年7月23日 第1版）

### 🚀 新功能

- **流程拓扑图章节**

  - 新增スマート薬局相关流程文档，包含薬師丸撫子和通用模块流程

  - 新增扫描仪连接结果获取流程详细文档，涵盖设备连接、扫描操作、PDF处理、QR识别、文件上传等完整自动化处理流程

  - 新增日志上传客户端流程文档，包含系统信息轮询、文件压缩和上传处理等完整流程

### 🎨 流程图标准化优化

- **Mermaid 流程图规范化**

  - 对扫描仪连接结果获取流程.md文件进行了完整的标准化优化

  - 对日志上传客户端流程.md文件进行了完整的标准化优化

### 📝 语法现代化改进

- **语法更新**

  - 将 `graph TD` 更新为现代 `flowchart TD` 语法

  - 统一使用 `-->|文本|` 替代旧的 `-- 文本 -->` 连接语法

  - 规范化节点形状：开始/结束使用 `([])`, 处理步骤使用 `[]`, 决策点使用 `{}`

- **样式标准化**

  - 应用统一的 `classDef` 样式类定义系统

  - 使用模板标准颜色方案（开始/结束：深蓝色 #1565c0，处理步骤：紫色 #6a1b9a，决策点：橙色 #ef6c00，错误：红色 #b71c1c）

  - 移除所有旧的内联样式，使用 `class` 应用样式类

### 🔧 语法兼容性修复

- **特殊字符处理**

  - 对包含括号 `()` 和下划线 `_` 的节点标签使用双引号包围

  - 修复了 Mermaid 解析器的语法冲突问题

  - 确保所有流程图能够正常预览和渲染

### ✅ 质量保证

- **流程完整性维护**

  - 保持所有原有流程逻辑和连接关系不变

  - 维护所有 subgraph 分组结构的完整性

  - 确保业务流程的准确性和可读性

---

**发布日期**：2025年7月23日

**版本类型**：流程图标准化版本

**维护团队**：NeoX 开发团队

## v2025072101（2025年7月21日 第1版）

### 🚀 新功能

- **流程拓扑图章节**

  - 新增医疗后端识别端流程详细文档，包含生命周期、架构、状态流转等

  - 新增 FAX 受付流程、GPU引擎识别流程、NSIPS、Smart Merge 等医疗后端相关流程

  - 新增薬師丸賢太处方笺保存匹配流程文档

  - 新增自动化运维相关流程文档，包括 medical-backend、performance、terraform

- **自动化运维章节**

  - 新增 Ansible Semaphore 自动化发布平台配置指南

### 📝 内容改进

- **文档导航结构优化**

  - 重新组织导航结构，增加主要章节分类

  - 优化子章节组织，提高文档查找效率

  - 调整文档层级，使结构更加清晰

- **首页内容更新**

  - 更新首页导航表格，增加新增章节的快速链接

  - 完善项目结构描述，反映最新的文件组织

  - 优化"如何使用本文档"部分，增加流程了解和自动化部署指南

### 🔧 文档结构完善

- **导航配置更新**

  - 在 `mkdocs.yml` 中完善导航配置，增加新章节

  - 调整章节顺序，优化用户浏览体验

  - 保持导航结构与实际文件结构一致

- **README 同步更新**

  - 更新项目主要内容描述，增加自动化运维和流程拓扑图内容

  - 完善项目结构图，反映最新的目录组织

  - 增加获取帮助部分的常见问题解答链接

### 🎯 文档覆盖范围扩展

- **系统架构文档**：新增多个系统架构和流程图文档，帮助开发者理解系统整体结构

- **自动化工具文档**：增加自动化部署和运维相关工具的使用指南

- **流程说明文档**：详细说明各个子系统的工作流程和数据流转

---

**发布日期**：2025年7月21日

**版本类型**：功能扩展版本

**维护团队**：NeoX 开发团队

## v2025063001（2025年6月30日 第1版）

### 🚀 新功能

- **常见问题解答（FAQ）**

  - 新增详细的FAQ文档，涵盖开发环境搭建中的常见问题

  - 包含Docker容器报错、API请求鉴权、网络连接问题等解决方案

  - 提供Windows系统特有问题的解决方法

### 📝 内容改进

- **项目描述优化**

  - 简化项目介绍，提高可读性

  - 更新仓库地址为具体的Bitbucket链接

  - 优化项目结构描述，使用通用格式

- **使用方式重构**

  - 重新组织README.md中的使用者和开发者说明

  - 使用者现在可直接打开HTML文件，无需安装任何工具

  - 开发者提供多种安装和启动方式选择

### 🔧 文档结构完善

- **导航优化**

  - 在主页添加FAQ章节的快速访问链接

  - 更新文档导航表格，包含常见问题解答条目

  - 保持文档结构的一致性和完整性

### 🎯 问题解决覆盖

FAQ文档包含以下常见问题的解决方案：

- **Docker容器报错处理**：composer扩展安装和.env配置

- **API请求鉴权设置**：公私钥对配置和Postman使用

- **ImageMagick挂载错误**：policy.xml挂载问题解决

- **Windows网络连接问题**：代理设置和手动安装方法

- **Composer安装报错**：依赖冲突和SSH密钥配置

---

**发布日期**：2025年6月30日

**版本类型**：功能增强版本

**维护团队**：NeoX 开发团队

## v2025062801（2025年6月28日 第1版）

### 🚀 新功能

- **文档系统初始化**

  - 建立了完整的开发文档体系

  - 采用 MkDocs Material 主题，提供现代化的文档界面

  - 支持中文本地化显示

- **后端开发环境搭建指南**

  - 详细的代码部署流程说明

  - AWS ECR 访问权限配置指导

  - Docker 开发环境部署步骤

  - 后端代码环境配置详解

### 📚 文档结构

- **开发环境搭建**

  - 后端开发完整流程文档

  - 分模块详细说明，便于查阅和维护

- **版本管理**

  - 建立规范的版本说明文档

  - 采用日期版本号格式（YYYYMMDDXX）

### 🔧 技术特性

- **文档框架**：MkDocs + Material 主题

- **语言支持**：完整中文支持

- **导航结构**：层级化章节组织

- **搜索功能**：全文搜索支持

- **响应式设计**：适配各种设备屏幕

### 📝 文档覆盖范围

- **代码部署**：Git 仓库管理和代码获取

- **权限配置**：AWS ECR 访问权限设置

- **环境搭建**：Docker 开发环境部署

- **项目配置**：后端服务配置和验证

### 🎯 下一步计划

- 添加更多开发环境配置说明

- 完善API文档

- 增加常见问题解答（FAQ）

- 补充最佳实践指南

---

**发布日期**：2025年6月28日

**版本类型**：初始版本

**维护团队**：NeoX 开发团队

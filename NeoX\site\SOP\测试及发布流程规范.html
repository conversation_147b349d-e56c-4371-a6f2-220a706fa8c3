
<!DOCTYPE html>

<html class="no-js" lang="zh">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width,initial-scale=1" name="viewport"/>
<meta content="Docs site for NeoX." name="description"/>
<meta content="SongLin Lu" name="author"/>
<link href="%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html" rel="prev"/>
<link href="%E4%BA%A7%E5%93%81%E7%89%88%E6%9C%AC%E5%91%BD%E5%90%8D%E8%A7%84%E8%8C%83.html" rel="next"/>
<link href="../assets/images/favicon.png" rel="icon"/>
<meta content="mkdocs-1.6.1, mkdocs-material-9.6.18" name="generator"/>
<title>测试及发布流程规范 - NeoX Docs</title>
<link href="../assets/stylesheets/main.7e37652d.min.css" rel="stylesheet"/>
<link href="../assets/stylesheets/palette.06af60db.min.css" rel="stylesheet"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&amp;display=fallback" rel="stylesheet"/>
<style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
<script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
</head>
<body data-md-color-accent="blue" data-md-color-primary="blue" data-md-color-scheme="default" dir="ltr">
<input autocomplete="off" class="md-toggle" data-md-toggle="drawer" id="__drawer" type="checkbox"/>
<input autocomplete="off" class="md-toggle" data-md-toggle="search" id="__search" type="checkbox"/>
<label class="md-overlay" for="__drawer"></label>
<div data-md-component="skip">
<a class="md-skip" href="#_1">
          跳转至
        </a>
</div>
<div data-md-component="announce">
</div>
<header class="md-header" data-md-component="header">
<nav aria-label="页眉" class="md-header__inner md-grid">
<a aria-label="NeoX Docs" class="md-header__button md-logo" data-md-component="logo" href="../index.html" title="NeoX Docs">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
<label class="md-header__button md-icon" for="__drawer">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"></path></svg>
</label>
<div class="md-header__title" data-md-component="header-title">
<div class="md-header__ellipsis">
<div class="md-header__topic">
<span class="md-ellipsis">
            NeoX Docs
          </span>
</div>
<div class="md-header__topic" data-md-component="header-topic">
<span class="md-ellipsis">
            
              测试及发布流程规范
            
          </span>
</div>
</div>
</div>
<form class="md-header__option" data-md-component="palette">
<input aria-hidden="true" class="md-option" data-md-color-accent="blue" data-md-color-media="" data-md-color-primary="blue" data-md-color-scheme="default" id="__palette_0" name="__palette" type="radio"/>
</form>
<script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
</nav>
</header>
<div class="md-container" data-md-component="container">
<nav aria-label="标签" class="md-tabs" data-md-component="tabs">
<div class="md-grid">
<ul class="md-tabs__list">
<li class="md-tabs__item">
<a class="md-tabs__link" href="../index.html">
        
  
  
    
  
  主页

      </a>
</li>
<li class="md-tabs__item md-tabs__item--active">
<a class="md-tabs__link" href="%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
          
  
  
  SOP

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html">
          
  
  
  开发环境搭建

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html">
          
  
  
  自动化运维

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
          
  
  
  流程拓扑图

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html">
          
  
  
  关于

        </a>
</li>
</ul>
</div>
</nav>
<main class="md-main" data-md-component="main">
<div class="md-main__inner md-grid">
<div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation">
<div class="md-sidebar__scrollwrap">
<div class="md-sidebar__inner">
<nav aria-label="导航栏" class="md-nav md-nav--primary md-nav--lifted md-nav--integrated" data-md-level="0">
<label class="md-nav__title" for="__drawer">
<a aria-label="NeoX Docs" class="md-nav__button md-logo" data-md-component="logo" href="../index.html" title="NeoX Docs">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
    NeoX Docs
  </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../index.html">
<span class="md-ellipsis">
    主页
    
  </span>
</a>
</li>
<li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
<input checked="" class="md-nav__toggle md-toggle" id="__nav_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="">
<span class="md-ellipsis">
    SOP
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="true" aria-labelledby="__nav_2_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_2">
<span class="md-nav__icon md-icon"></span>
            SOP
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    项目研发管理流程规范
    
  </span>
</a>
</li>
<li class="md-nav__item md-nav__item--active">
<input class="md-nav__toggle md-toggle" id="__toc" type="checkbox"/>
<label class="md-nav__link md-nav__link--active" for="__toc">
<span class="md-ellipsis">
    测试及发布流程规范
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<a class="md-nav__link md-nav__link--active" href="%E6%B5%8B%E8%AF%95%E5%8F%8A%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    测试及发布流程规范
    
  </span>
</a>
<nav aria-label="目录" class="md-nav md-nav--secondary">
<label class="md-nav__title" for="__toc">
<span class="md-nav__icon md-icon"></span>
      目录
    </label>
<ul class="md-nav__list" data-md-component="toc" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="#_2">
<span class="md-ellipsis">
      第一部分：基础框架
    </span>
</a>
<nav aria-label="第一部分：基础框架" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#11">
<span class="md-ellipsis">
      1.1 总体目标
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#12">
<span class="md-ellipsis">
      1.2 指导原则
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#13">
<span class="md-ellipsis">
      1.3 适用范围与例外
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#14">
<span class="md-ellipsis">
      1.4 术语表
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#raci">
<span class="md-ellipsis">
      第二部分：角色与职责矩阵 (RACI)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_3">
<span class="md-ellipsis">
      第三部分：端到端工作流程
    </span>
</a>
<nav aria-label="第三部分：端到端工作流程" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#31-process-flow-diagram">
<span class="md-ellipsis">
      3.1 流程图 (Process Flow Diagram)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#32">
<span class="md-ellipsis">
      3.2 阶段一：规划、开发与预测试
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#33">
<span class="md-ellipsis">
      3.3 阶段二：正式质量保证
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#34">
<span class="md-ellipsis">
      3.4 阶段三：生产发布与验证
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_4">
<span class="md-ellipsis">
      第四部分：工具与产物标准
    </span>
</a>
<nav aria-label="第四部分：工具与产物标准" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#41-jira">
<span class="md-ellipsis">
      4.1 Jira治理策略
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#42-definition-of-ready-dor-for-qa">
<span class="md-ellipsis">
      4.2 “就绪定义” (Definition of Ready - DoR) for QA
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_5">
<span class="md-ellipsis">
      第五部分：发布治理与策略
    </span>
</a>
<nav aria-label="第五部分：发布治理与策略" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#51">
<span class="md-ellipsis">
      5.1 发布节奏与时间表
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#52">
<span class="md-ellipsis">
      5.2 非周期性与紧急发布协议
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#53">
<span class="md-ellipsis">
      5.3 “双人原则”：研发与部署的职责分离
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_6">
<span class="md-ellipsis">
      第六部分：附录
    </span>
</a>
<nav aria-label="第六部分：附录" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#61">
<span class="md-ellipsis">
      6.1 流程例外：研发主导的自发布协议
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#62-escalation-paths">
<span class="md-ellipsis">
      6.2 升级路径 (Escalation Paths)
    </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="%E4%BA%A7%E5%93%81%E7%89%88%E6%9C%AC%E5%91%BD%E5%90%8D%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    产品版本命名规范
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_3" type="checkbox"/>
<label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
<span class="md-ellipsis">
    开发环境搭建
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_3_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_3">
<span class="md-nav__icon md-icon"></span>
            开发环境搭建
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_3_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_3_1" id="__nav_3_1_label" tabindex="0">
<span class="md-ellipsis">
    后端开发
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_3_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_3_1">
<span class="md-nav__icon md-icon"></span>
            后端开发
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html">
<span class="md-ellipsis">
    代码部署
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/AWS-ECR%E6%9D%83%E9%99%90%E8%AE%BE%E7%BD%AE.html">
<span class="md-ellipsis">
    AWS ECR权限设置
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/Docker%E7%8E%AF%E5%A2%83%E9%83%A8%E7%BD%B2.html">
<span class="md-ellipsis">
    Docker环境部署
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E5%90%8E%E7%AB%AF%E4%BB%A3%E7%A0%81%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE.html">
<span class="md-ellipsis">
    后端代码环境配置
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/FAQ.html">
<span class="md-ellipsis">
    常见问题解答
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_4" type="checkbox"/>
<label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
<span class="md-ellipsis">
    自动化运维
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_4_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_4">
<span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_4_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_4_1" id="__nav_4_1_label" tabindex="0">
<span class="md-ellipsis">
    自动化发布平台
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_4_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_4_1">
<span class="md-nav__icon md-icon"></span>
            自动化发布平台
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html">
<span class="md-ellipsis">
    Ansible Semaphore
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5" type="checkbox"/>
<label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
<span class="md-ellipsis">
    流程拓扑图
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_5">
<span class="md-nav__icon md-icon"></span>
            流程拓扑图
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_1" id="__nav_5_1_label" tabindex="0">
<span class="md-ellipsis">
    医疗后端
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_1">
<span class="md-nav__icon md-icon"></span>
            医疗后端
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_1_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_1_1" id="__nav_5_1_1_label" tabindex="0">
<span class="md-ellipsis">
    识别端流程
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_1_1_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_1_1">
<span class="md-nav__icon md-icon"></span>
            识别端流程
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
<span class="md-ellipsis">
    01-生命周期
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/02-%E6%95%B4%E4%BD%93%E6%9E%B6%E6%9E%84.html">
<span class="md-ellipsis">
    02-整体架构
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/03-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
<span class="md-ellipsis">
    03-识别任务的生命周期
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/04-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%8A%B6%E6%80%81%E6%B5%81%E8%BD%AC.html">
<span class="md-ellipsis">
    04-识别任务状态流转
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/05-%E7%94%A8%E4%BA%8E%E7%BB%93%E6%9E%9C%E8%BD%AE%E8%AF%A2%E7%9A%84Redis%E7%BC%93%E5%AD%98.html">
<span class="md-ellipsis">
    05-用于结果轮询的Redis缓存
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/06-%E6%9C%8D%E5%8A%A1%E5%88%86%E5%B1%82%E6%9E%B6%E6%9E%84-%E7%BB%84%E4%BB%B6.html">
<span class="md-ellipsis">
    06-服务分层架构-组件
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/07-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1task%E4%BF%A1%E6%81%AF%E8%AF%A6%E8%BF%B0.html">
<span class="md-ellipsis">
    07-识别任务task信息详述
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/08-%E5%A4%84%E6%96%B9%E5%90%88%E5%B9%B6%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    08-处方合并流程
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/09-FAX%E5%8F%97%E4%BB%98%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    09-FAX受付流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E5%A4%84%E6%96%B9%E8%B0%83%E5%BA%A6%E5%99%A8%E6%9C%BA%E5%88%B6.html">
<span class="md-ellipsis">
    处方调度器机制
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/NSIPS.html">
<span class="md-ellipsis">
    NSIPS
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/GPU%E5%BC%95%E6%93%8E%E8%AF%86%E5%88%AB%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    GPU 引擎识别流程
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/Smart-Merge.html">
<span class="md-ellipsis">
    Smart Merge
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_2" id="__nav_5_2_label" tabindex="0">
<span class="md-ellipsis">
    薬師丸賢太
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_2_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_2">
<span class="md-nav__icon md-icon"></span>
            薬師丸賢太
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%96%AC%E5%B8%AB%E4%B8%B8%E8%B3%A2%E5%A4%AA/%E5%A4%84%E6%96%B9%E7%AC%BA%E4%BF%9D%E5%AD%98%E5%8C%B9%E9%85%8D%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    处方笺保存匹配流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3" id="__nav_5_3_label" tabindex="0">
<span class="md-ellipsis">
    スマート薬局
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_3">
<span class="md-nav__icon md-icon"></span>
            スマート薬局
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3_1" id="__nav_5_3_1_label" tabindex="0">
<span class="md-ellipsis">
    薬師丸撫子
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_1_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_3_1">
<span class="md-nav__icon md-icon"></span>
            薬師丸撫子
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E8%96%AC%E5%B8%AB%E4%B8%B8%E6%92%AB%E5%AD%90/%E6%89%AB%E6%8F%8F%E4%BB%AA%E8%BF%9E%E6%8E%A5%E7%BB%93%E6%9E%9C%E8%8E%B7%E5%8F%96%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    扫描仪连接结果获取流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3_2" id="__nav_5_3_2_label" tabindex="0">
<span class="md-ellipsis">
    通用模块
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_2_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_3_2">
<span class="md-nav__icon md-icon"></span>
            通用模块
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E9%80%9A%E7%94%A8%E6%A8%A1%E5%9D%97/%E6%97%A5%E5%BF%97%E4%B8%8A%E4%BC%A0%E5%AE%A2%E6%88%B7%E7%AB%AF%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    日志上传客户端流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_4" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_4" id="__nav_5_4_label" tabindex="0">
<span class="md-ellipsis">
    自动化运维
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_4_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_4">
<span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/medical-backend.html">
<span class="md-ellipsis">
    medical-backend
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/performance.html">
<span class="md-ellipsis">
    performance
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/terraform.html">
<span class="md-ellipsis">
    terraform
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_6" type="checkbox"/>
<label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
<span class="md-ellipsis">
    关于
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_6_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_6">
<span class="md-nav__icon md-icon"></span>
            关于
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html">
<span class="md-ellipsis">
    版本说明
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</div>
</div>
</div>
<div class="md-content" data-md-component="content">
<article class="md-content__inner md-typeset">
<h1 id="_1">测试及发布流程规范<a class="headerlink" href="#_1" title="Permanent link">¶</a></h1>
<h2 id="_2"><strong>第一部分：基础框架</strong><a class="headerlink" href="#_2" title="Permanent link">¶</a></h2>
<p>本章节旨在建立流程的理论基础，通过阐述核心目标、指导原则、适用范围及关键术语，确保所有团队成员对流程有统一、明确的理解，并在此共识上协同工作。</p>
<h3 id="11"><strong>1.1 总体目标</strong><a class="headerlink" href="#11" title="Permanent link">¶</a></h3>
<p>本标准作业程序（SOP）旨在为公司所有软件产品的测试与发布活动，建立一个系统化、可预测且高质量的管理流程。其核心目标是通过严谨的分析、验证与确认手段，评估产品的质量与可靠性，主动识别并缓解潜在风险，最终确保每一次部署到生产环境的变更都能增强系统稳定性，并为客户创造价值。原则上，任何未经本SOP规定流程测试的程序或产品，均不得发布至生产环境。</p>
<h3 id="12"><strong>1.2 指导原则</strong><a class="headerlink" href="#12" title="Permanent link">¶</a></h3>
<p>本SOP的有效执行依赖于以下核心原则，它们是贯穿整个流程的文化与行为准则：</p>
<ul>
<li><strong>质量共担原则 (Quality as a Shared Responsibility)</strong>：质量保证（QA）团队负责管控和实施测试流程，但构建高质量的产品是产品、研发、测试及运维团队的共同责任。从需求定义到上线维护，每个环节的参与者都需为产品的最终质量负责。  </li>
<li><strong>记录即沟通原则 (Communication via Record)</strong>：所有关键的沟通、决策、确认及变更，都必须在Jira等指定的项目管理工具中留下明确的文本记录。口头协议、即时通讯工具中的讨论不能作为流程推进的依据。此原则确保了所有操作的可追溯性、透明度和问责性。  </li>
<li><strong>质量左移原则 (Shift-Left Quality)</strong>：鼓励在开发生命周期的早期阶段就注入质量保障措施。这不仅是QA团队的职责，更是研发团队的份内工作。要求研发人员进行详尽的自测并提供报告、提前准备回滚方案等，都是此原则的具体体现。其目的是尽早发现和修复问题，降低后期修复成本和风险。  </li>
<li><strong>风险驱动的发布节奏原则 (Risk-Based Release Cadence)</strong>：所有发布活动的时间安排，尤其是针对重要产品设置的发布截止时间（如北京时间17:30），其根本目的是为了最小化对客户业务的潜在影响，并确保在发布后有充足的人力进行监控和应对可能发生的突发事件。  </li>
<li><strong>可验证的审计追踪原则 (Verifiable Audit Trail)</strong>：强制要求在Jira中建立从开发任务票、到QA测试票、再到上线票的清晰链接关系。这构建了一条完整的审计链，对于问题排查、责任界定、流程优化以及事后复盘至关重要。</li>
</ul>
<h3 id="13"><strong>1.3 适用范围与例外</strong><a class="headerlink" href="#13" title="Permanent link">¶</a></h3>
<p>本SOP适用于公司内所有产品线的内部测试与发布工作，涵盖了新功能开发、缺陷修复、配置变更等所有旨在部署到共享测试环境或生产环境的软件变更。</p>
<p>任何对本流程的偏离都必须遵循既定的例外处理机制。一个明确的例外是，部分特殊需求（如仅涉及算法精度调优）可由研发团队在自测后自行发布。然而，此类活动也必须在Jira中创建任务并进行完整记录，其具体操作规程详见本文档附录。</p>
<h3 id="14"><strong>1.4 术语表</strong><a class="headerlink" href="#14" title="Permanent link">¶</a></h3>
<p>为确保沟通的精确性，以下关键术语在本SOP中具有特定含义：</p>
<ul>
<li><strong>重要产品 (Important Product)</strong>：指因其对客户收入有直接影响、拥有大量活跃用户，或作为其他重要产品的关键上游依赖而被官方指定为一级（Tier 1）的任何产品或服务。例如，文档中提及的“贤太”即为此类产品。重要产品的清单由技术委员会或产品领导团队维护，并有明确的文档记录。  </li>
<li><strong>自测报告 (Self-Test Report)</strong>：由研发人员编写，用以证明其开发的功能在开发环境中按预期工作的证据集合。形式可以是截图、屏幕录像、日志文件、单元测试结果或Confluence页面链接等，但必须包含明确的自测完成时间信息。  </li>
<li><strong>回滚手顺 (Rollback Procedure)</strong>：一份详细、可执行的技术文档，指导运维人员在必要时能够安全、快速地将已部署的变更从生产环境撤销，使系统恢复至发布前的稳定状态。在可行的情况下，回滚方案本身也应经过测试。  </li>
<li><strong>代码冻结 (Code Freeze)</strong>：指在发布周期中的一个特定时间点，在此之后，除了修复严重影响发布的关键缺陷（Release Blocker）外，任何新的功能性或非关键性代码变更都不得合入发布分支。  </li>
<li><strong>职责分离 (Separation of Duties)</strong>：一项核心的风险控制策略，要求执行生产环境部署的人员（上线人员）不得是该功能点的主要研发人员。此举旨在通过引入独立的审查视角来降低部署风险。</li>
</ul>
<h2 id="raci"><strong>第二部分：角色与职责矩阵 (RACI)</strong><a class="headerlink" href="#raci" title="Permanent link">¶</a></h2>
<p>为明确各角色在软件测试与发布流程中的具体职责，消除模糊地带，特制定以下RACI矩阵。RACI是项目管理中的一种工具，用于定义谁负责（Responsible）、谁批准（Accountable）、谁被咨询（Consulted）以及谁被告知（Informed）。</p>
<table>
<thead>
<tr>
<th style="text-align: left;">流程阶段</th>
<th style="text-align: left;">产品经理 (PM)</th>
<th style="text-align: left;">研发工程师 (Dev)</th>
<th style="text-align: left;">质量保证工程师 (QA)</th>
<th style="text-align: left;">运维工程师 (Ops)</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;"><strong>1. 需求定义与排期</strong></td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>2. 功能开发与单元测试</strong></td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>3. 研发自测与报告准备</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>4. 代码审查 (Code Review)</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>5. 提交测试请求</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">R</td>
<td style="text-align: left;">A</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>6. 测试用例设计</strong></td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>7. 测试执行与缺陷报告</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>8. 缺陷修复</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>9. 回归测试</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">I</td>
</tr>
<tr>
<td style="text-align: left;"><strong>10. QA签核与测试报告</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">C</td>
</tr>
<tr>
<td style="text-align: left;"><strong>11. 发布排期与Go/No-Go决策</strong></td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
</tr>
<tr>
<td style="text-align: left;"><strong>12. 生产环境部署</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
</tr>
<tr>
<td style="text-align: left;"><strong>13. 线上功能验证</strong></td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
<td style="text-align: left;">C</td>
</tr>
<tr>
<td style="text-align: left;"><strong>14. 发布后监控</strong></td>
<td style="text-align: left;">I</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">C</td>
<td style="text-align: left;">R/A</td>
</tr>
</tbody>
</table>
<hr/>
<h2 id="_3"><strong>第三部分：端到端工作流程</strong><a class="headerlink" href="#_3" title="Permanent link">¶</a></h2>
<p>本章节详细描述了从需求提出到功能上线的完整生命周期，共分为三个主要阶段：规划与开发、质量保证、发布与验证。</p>
<h3 id="31-process-flow-diagram"><strong>3.1 流程图 (Process Flow Diagram)</strong><a class="headerlink" href="#31-process-flow-diagram" title="Permanent link">¶</a></h3>
<p>以下流程图直观地展示了各角色（泳道）之间的协作关系和工作流转。</p>
<div class="mermaid">flowchart TD
    subgraph "产品管理 (PM)"
        A1[需求收集/产品规划] --&gt; A2{需求评审}
        A2 --&gt; A3[排期 &amp; 创建Jira任务]
    end

    subgraph "研发 (Development)"
        B0[拟定技术方案] --&gt; B0_1{技术方案审查}
        B0_1 --&gt;|审查通过| B1[功能开发 &amp; 单元测试]
        B0_1 --&gt;|审查不通过| B0
        B1 --&gt; B2[代码审查]
        B2 --&gt; B3[执行自测 &amp; 准备自测报告]
        B3 --&gt; B4[准备回滚方案]
        B4 --&gt; B5[提交测试请求]
        B6[BUG修复] --&gt; B2
    end

    subgraph "质量保证 (QA)"
        C1[测试用例设计与评审] --&gt; C2[接收测试请求 &amp; DoR检查]
        C2 --&gt; C3[测试环境验证]
        C3 --&gt; C4[执行功能/集成测试]
        C4 --&gt; C5{测试结果}
        C5 --&gt;|发现BUG| C6[创建BUG票]
        C5 --&gt;|测试通过| C7[执行回归测试]
        C6 --&gt; B6
        C7 --&gt; C8[输出测试报告 &amp; QA签核]
    end

    subgraph "发布与运维 (Release &amp; Ops)"
        D1[创建上线票 &amp; 关联QA票] --&gt; D2{发布前最终会议}
        D2 --&gt;|Go决策| D3[执行生产发布]
        D2 --&gt;|No-Go决策| D4[延期发布]
        D3 --&gt; D5[生产环境验证]
        D5 --&gt; D6([发布完成 &amp; 启动监控])
        D4 --&gt; B6
    end

    %% 主要流程连接
    A3 --&gt; B0
    A3 --&gt; C1
    B5 --&gt; C2
    C2 --&gt;|DoR通过| C3
    C2 --&gt;|DoR驳回| B3
    C8 --&gt; D1

    %% 样式类定义
    classDef pmStyle fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef devStyle fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef qaStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef opsStyle fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef bugFix fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef endNode fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32

    %% 应用样式类
    class A1,A2,A3 pmStyle
    class B0,B1,B2,B3,B4,B5 devStyle
    class C1,C2,C3,C4,C7,C8 qaStyle
    class D1,D3,D5 opsStyle
    class A2,B0_1,C5,D2 decision
    class B6,C6,D4 bugFix
    class D6 endNode
</div>
<h3 id="32"><strong>3.2 阶段一：规划、开发与预测试</strong><a class="headerlink" href="#32" title="Permanent link">¶</a></h3>
<p>此阶段涵盖了从需求确认到正式提交测试前的所有研发活动。</p>
<ol>
<li><strong>需求定义与排期 (PM)</strong>：产品经理负责定义业务需求（PRD），组织需求评审会议，并在评审通过后，将需求拆解为可执行的任务，录入Jira系统并进行排期。  </li>
<li><strong>功能开发与单元测试 (Dev)</strong>：研发工程师根据Jira任务进行编码实现，并编写充分的单元测试以保证代码模块级别的质量。  </li>
<li><strong>代码审查 (Dev)</strong>：在提交测试前，代码必须经过至少一位同事的审查（Peer Review）。审查人需对代码质量、规范性、可维护性负责，其姓名和审查完成的记录必须在Jira任务中明确标注。  </li>
<li><strong>自测与产物准备 (Dev)</strong>：研发工程师必须在本地或开发测试环境中对所开发的功能进行全面的自测，并依据测试结果编写《自测报告》。同时，必须编写或更新对应的《回滚手顺》。这两份文档是提交测试的必要条件。</li>
</ol>
<h3 id="33"><strong>3.3 阶段二：正式质量保证</strong><a class="headerlink" href="#33" title="Permanent link">¶</a></h3>
<p>此阶段是QA团队主导的、系统性的质量验证过程。</p>
<ol>
<li><strong>测试请求与DoR检查 (QA)</strong>：研发工程师完成阶段一所有工作后，在Jira中将任务状态流转至QA。QA工程师接收到请求后，将首先根据《就绪定义 (DoR)》清单（详见4.2章节）进行检查。任何不满足DoR要求的提测都将被驳回，并附带明确的理由。  </li>
<li><strong>测试执行 (QA)</strong>：在测试环境验证无误后，QA工程师依据测试用例，执行功能测试、集成测试、接口测试等。所有发现的缺陷都必须在Jira中创建BUG票，并清晰描述复现步骤、预期与实际结果，同时关联到对应的开发任务票。  </li>
<li><strong>缺陷修复与回归循环 (Dev &amp; QA)</strong>：研发工程师根据BUG票修复缺陷，完成后再次提交测试。QA工程师在验证缺陷已被修复的同时，需要执行相应的回归测试，以确保修复没有引入新的问题。此过程可能多次迭代，直至产品质量达到发布标准。  </li>
<li><strong>QA签核 (QA Sign-off)</strong>：当所有主要功能通过测试，所有严重级别的缺陷都已关闭或经相关方确认可延后修复，且回归测试通过后，QA团队将出具正式的《测试报告》。该报告总结了测试范围、过程、结果和遗留风险。同时，QA会在Jira中对测试票进行“签核”（Sign-off），标志着该功能在质量上已具备发布条件。</li>
</ol>
<h3 id="34"><strong>3.4 阶段三：生产发布与验证</strong><a class="headerlink" href="#34" title="Permanent link">¶</a></h3>
<p>此阶段是将通过测试的功能安全、可靠地部署到生产环境的过程。</p>
<ol>
<li><strong>发布票创建与排期 (Ops/Release Manager)</strong>：运维或发布负责人创建上线票，并必须在票中关联已经获得QA签核的测试票。随后，与产品、研发、QA团队协调，根据发布策略（见5.1章节）确定最终的发布时间窗口。  </li>
<li><strong>Go/No-Go决策会议 (All Stakeholders)</strong>：对于所有重要产品或重大功能变更，在预定发布时间点前，必须召开一个简短的Go/No-Go会议。参会人员包括核心研发、QA、运维及产品代表。会议的目的是最后一次确认所有前置条件均已满足，并对发布风险达成共识。  </li>
<li><strong>生产部署 (Ops)</strong>：部署操作严格遵循“职责分离”原则，由运维工程师（非功能研发人员）执行。运维人员需严格按照上线票中提供的部署步骤和回滚方案进行操作。如对流程有任何疑问，必须暂停发布，并立即与研发、QA沟通确认。  </li>
<li><strong>线上验证 (QA/Dev)</strong>：部署完成后，QA或研发人员需要立即登录生产环境，对上线功能的核心路径进行一次快速的烟雾测试（Smoke Test），确保其基本可用。  </li>
<li><strong>发布完成与监控</strong>：线上验证通过后，正式宣告发布完成。运维团队将启动对相关系统指标的重点监控，以确保服务在发布后的稳定性。</li>
</ol>
<h2 id="_4"><strong>第四部分：工具与产物标准</strong><a class="headerlink" href="#_4" title="Permanent link">¶</a></h2>
<p>本章节旨在规范化流程中使用的工具和必须产出的文档，确保信息流转的标准化和高效性。</p>
<h3 id="41-jira"><strong>4.1 Jira治理策略</strong><a class="headerlink" href="#41-jira" title="Permanent link">¶</a></h3>
<p>Jira作为本流程的核心协作平台，其使用必须遵循以下强制性规范：</p>
<ul>
<li><strong>强制性票据层级与链接</strong>：所有工作必须遵循 开发任务 → QA测试任务 → 上线任务 的严格链接结构。不允许存在孤立的、无法追溯源头的测试或上线票。这一结构是构建完整审计追踪链的基础。  </li>
<li><strong>标准化的状态流转</strong>：Jira工作流中的每一个状态（如“待办”、“开发中”、“待测试”、“已解决”、“已关闭”）都有明确的定义和准入/准出条件。例如，只有QA工程师有权限将票据状态从“测试中”变更为“待发布”。  </li>
<li><strong>信息集中化</strong>：所有与任务相关的信息，包括但不限于需求细节、讨论记录、代码diff链接、算法审核人及时间、确认记录等，都必须记录在Jira票据的描述或评论区中。这确保Jira成为该任务唯一的、权威的信息源（Single Source of Truth）。</li>
</ul>
<h3 id="42-definition-of-ready-dor-for-qa"><strong>4.2 “就绪定义” (Definition of Ready - DoR) for QA</strong><a class="headerlink" href="#42-definition-of-ready-dor-for-qa" title="Permanent link">¶</a></h3>
<p>“就绪定义”是QA团队接收测试请求时的一道正式质量门。它将提测方（研发）的责任和交付标准明确化，减少了因信息不全导致的沟通成本和返工。只有满足以下所有条件的测试请求，才被认为是“就绪”的。</p>
<table>
<thead>
<tr>
<th style="text-align: left;">#</th>
<th style="text-align: left;">检查项</th>
<th style="text-align: left;">状态</th>
<th style="text-align: left;">备注</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;">1</td>
<td style="text-align: left;">开发Jira任务已链接至本QA任务</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">遵循 开发 → QA 的链接规则</td>
</tr>
<tr>
<td style="text-align: left;">2</td>
<td style="text-align: left;">代码已完成同行评审 (Code Review)</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">评审人姓名及完成记录已在开发票中注明</td>
</tr>
<tr>
<td style="text-align: left;">3</td>
<td style="text-align: left;">代码仓库的Diff链接已提供</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">链接需填写在开发任务票中</td>
</tr>
<tr>
<td style="text-align: left;">4</td>
<td style="text-align: left;">详尽的《自测报告》已附加或链接</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">报告必须包含明确的自测时间</td>
</tr>
<tr>
<td style="text-align: left;">5</td>
<td style="text-align: left;">最终版的《回滚手顺》链接已提供</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">如无回滚方案，需提供可接受的理由并获批准</td>
</tr>
<tr>
<td style="text-align: left;">6</td>
<td style="text-align: left;">目标测试业务与预期结果已清晰描述</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">确保QA能理解并正确操作</td>
</tr>
<tr>
<td style="text-align: left;">7</td>
<td style="text-align: left;">目标测试环境已明确指定</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">如开发测试环境、生产环境等</td>
</tr>
<tr>
<td style="text-align: left;">8</td>
<td style="text-align: left;">已向QA进行简要的功能演示或讲解</td>
<td style="text-align: left;">☐</td>
<td style="text-align: left;">确保信息对齐，提高测试效率</td>
</tr>
</tbody>
</table>
<h2 id="_5"><strong>第五部分：发布治理与策略</strong><a class="headerlink" href="#_5" title="Permanent link">¶</a></h2>
<p>本章节整合了所有与生产发布直接相关的规则和策略，为发布决策和执行提供清晰的指导。</p>
<h3 id="51"><strong>5.1 发布节奏与时间表</strong><a class="headerlink" href="#51" title="Permanent link">¶</a></h3>
<p>为平衡业务需求与系统风险，不同类型产品的发布需遵循不同的时间窗口策略。</p>
<table>
<thead>
<tr>
<th style="text-align: left;">产品类别</th>
<th style="text-align: left;">标准发布窗口</th>
<th style="text-align: left;">发布截止时间 (北京时间)</th>
<th style="text-align: left;">部署人员要求</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;"><strong>重要产品</strong></td>
<td style="text-align: left;">需与各方协调安排</td>
<td style="text-align: left;"><strong>17:30</strong></td>
<td style="text-align: left;">严格的职责分离</td>
</tr>
<tr>
<td style="text-align: left;"><strong>非重要产品</strong></td>
<td style="text-align: left;">工作时间内皆可</td>
<td style="text-align: left;">与QA协调即可</td>
<td style="text-align: left;">严格的职责分离</td>
</tr>
</tbody>
</table>
<p>任何需要在标准时间之外进行的发布，均被视为特殊情况，必须提前与所有相关方（尤其是QA和运维）沟通协调，并获得批准。</p>
<h3 id="52"><strong>5.2 非周期性与紧急发布协议</strong><a class="headerlink" href="#52" title="Permanent link">¶</a></h3>
<ul>
<li><strong>定义</strong>：紧急发布专用于处理对业务构成严重威胁的突发事件，例如：生产环境服务宕机、严重安全漏洞、导致核心功能不可用的关键缺陷等。  </li>
<li><strong>流程</strong>：紧急发布可绕过部分标准流程（如完整的回归测试），但必须遵循一个加速的审批和执行流程。  </li>
<li><strong>发起</strong>：由研发或运维负责人发起，并立即创建紧急Jira票。  </li>
<li><strong>评估</strong>：由技术负责人、核心研发和QA负责人共同快速评估变更的必要性、风险和影响。  </li>
<li><strong>审批</strong>：必须获得技术负责人（或CTO）的明确批准。  </li>
<li><strong>执行</strong>：部署仍需遵循“职责分离”原则，并有详细的记录。  </li>
<li><strong>事后</strong>：发布后需进行完整的复盘，并补全相关文档。</li>
</ul>
<h3 id="53"><strong>5.3 “双人原则”：研发与部署的职责分离</strong><a class="headerlink" href="#53" title="Permanent link">¶</a></h3>
<p>本SOP强制要求，执行生产环境部署的人员不能是待发布功能的主要研发人员。这一原则，也称为“双人原则”或“四眼原则”，其背后的风险管理逻辑在于：</p>
<ol>
<li><strong>降低操作失误风险</strong>：由一个不熟悉代码细节但熟悉部署流程的人来执行操作，更能严格遵循部署文档，一双“新鲜的眼睛”更容易发现文档或流程中的疏漏。  </li>
<li><strong>防止未经授权的变更</strong>：杜绝了研发人员在最后时刻绕过测试和审查，直接部署未经审核的代码的可能。  </li>
<li><strong>促进文档质量提升</strong>：研发人员必须编写出清晰、完整、他人能够独立理解和执行的部署与回滚文档，从而提升了知识传递的质量和团队的韧性。</li>
</ol>
<p>如果上线人员对部署内容或流程有任何疑议，必须暂停发布，并召开由QA、研发、上线人员共同参与的沟通会议。只有在所有疑虑都得到解决，并由技术负责人确认风险可控后，方可继续发布。</p>
<h2 id="_6"><strong>第六部分：附录</strong><a class="headerlink" href="#_6" title="Permanent link">¶</a></h2>
<h3 id="61"><strong>6.1 流程例外：研发主导的自发布协议</strong><a class="headerlink" href="#61" title="Permanent link">¶</a></h3>
<p>虽然标准流程要求所有变更都需经过QA测试，但对于某些特定类型的变更，允许研发团队在自测后直接发布。此例外旨在提高特定场景下的迭代效率，但必须在严格的框架内进行，以控制风险。</p>
<ul>
<li><strong>适用场景</strong>：此协议仅适用于那些不涉及API变更、不改变核心业务逻辑、不影响用户界面的“精度相关功能点”。典型例子包括：  </li>
<li>机器学习模型权重文件的更新。  </li>
<li>不改变输入输出结构的算法参数调优。  </li>
<li>数据ETL脚本中与数据质量相关的逻辑微调。  </li>
<li><strong>审批流程</strong>：  </li>
<li>研发人员必须在Jira中创建专门的“自发布”类型任务。  </li>
<li>在任务描述中，必须详细说明变更内容、自测方案、评估指标以及为何适用于此例外协议。  </li>
<li>变更必须获得直属技术负责人（Tech Lead）和对应产品经理（PM）的共同批准，批准记录需留在Jira评论中。  </li>
<li><strong>最低要求</strong>：即使豁免了QA的完整测试，也必须满足以下最低质量保证要求：  </li>
<li><strong>详尽的自测报告</strong>：报告需重点展示变更前后的关键指标对比（如模型准确率、召回率等）。  </li>
<li><strong>同行评审 (Peer Review)</strong>：代码或配置变更必须由另一位具备相应能力的同事进行评审。  </li>
<li><strong>完备的回滚方案</strong>：必须提供并验证过可以快速回滚此次变更的方案。  </li>
<li><strong>完整的Jira记录</strong>：所有操作、决策和产出物都必须在Jira任务中记录，以备审计。</li>
</ul>
<h3 id="62-escalation-paths"><strong>6.2 升级路径 (Escalation Paths)</strong><a class="headerlink" href="#62-escalation-paths" title="Permanent link">¶</a></h3>
<p>在流程执行过程中，如遇分歧或障碍，应遵循以下路径进行沟通和决策升级，以确保问题得到高效、公正的解决。</p>
<ul>
<li><strong>关于“就绪定义(DoR)”的争议</strong>：  </li>
<li>第一层：当事研发人员与QA工程师。  </li>
<li>升级至：QA负责人。  </li>
<li><strong>关于缺陷优先级的争议</strong>：  </li>
<li>第一层：当事研发人员、QA工程师与产品经理。  </li>
<li>升级至：研发负责人与产品负责人。  </li>
<li><strong>关于发布时间或Go/No-Go决策的争议</strong>：  </li>
<li>第一层：核心研发、QA、运维及产品代表。  </li>
<li>升级至：技术负责人或工程总监。  </li>
<li><strong>关于本SOP流程本身的解释或适用性的争议</strong>：  </li>
<li>升级至：流程制定与维护团队（如技术管理委员会）。</li>
</ul>
</article>
</div>
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
</div>
<button class="md-top md-icon" data-md-component="top" hidden="" type="button">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"></path></svg>
  回到页面顶部
</button>
</main>
<footer class="md-footer">
<div class="md-footer-meta md-typeset">
<div class="md-footer-meta__inner md-grid">
<div class="md-copyright">
<div class="md-copyright__highlight">
      Copyright © 2025 SongLin Lu. All rights reserved.
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" rel="noopener" target="_blank">
      Material for MkDocs
    </a>
</div>
</div>
</div>
</footer>
</div>
<div class="md-dialog" data-md-component="dialog">
<div class="md-dialog__inner md-typeset"></div>
</div>
<script id="__config" type="application/json">{"base": "..", "features": ["navigation.sections", "navigation.expand", "navigation.tabs", "navigation.top", "navigation.tracking", "toc.follow", "toc.integrate"], "search": "../assets/javascripts/workers/search.973d3a69.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": null}</script>
<script src="../assets/javascripts/bundle.92b07e13.min.js"></script>
<script type="module">import mermaid from "https://unpkg.com/mermaid@10.4.0/dist/mermaid.esm.min.mjs";
mermaid.initialize({});</script></body>
</html>
#!/usr/bin/env pwsh

# ==============================================================================
#
# release-tag.ps1: 一个用于在main分支上创建并推送附注标签的稳健脚本。
#
# 用法: .\release-tag.ps1 <tag_version>
#
# 描述:
#   此脚本自动化了版本发布的标签创建过程，并包含了一系列严格的前置检查
#   以确保发布的安全性、准确性和可重复性。
#
# 前置检查包括:
#   1. 确保提供了标签参数。
#   2. 验证当前目录是一个Git仓库。
#   3. 强制要求当前分支必须是 'main'。
#   4. 检查工作目录是否干净（无未提交的更改）。
#   5. 确保本地 'main' 分支与远程 'origin/main' 完全同步。
#   6. 检查标签是否已在本地或远程存在，防止冲突。
#
# ==============================================================================

# 严格模式: 遇到错误时立即退出
$ErrorActionPreference = "Stop"

# --- 配置 ---
# 目标发布分支
$MAIN_BRANCH = "main"
# 远程仓库名称
$REMOTE_NAME = "origin"

# --- 颜色定义，用于输出 ---
$COLOR_RED = "Red"
$COLOR_GREEN = "Green"
$COLOR_YELLOW = "Yellow"
$COLOR_BLUE = "Blue"

# --- 辅助函数 ---

# 错误退出函数
function Error-Exit {
    param([string]$Message)
    Write-Host "错误: $Message" -ForegroundColor $COLOR_RED
    exit 1
}

# 成功消息函数
function Success-Msg {
    param([string]$Message)
    Write-Host "✓ $Message" -ForegroundColor $COLOR_GREEN
}

# 步骤消息函数
function Step-Msg {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $COLOR_BLUE
}

# 警告消息函数
function Warning-Msg {
    param([string]$Message)
    Write-Host "⚠ $Message" -ForegroundColor $COLOR_YELLOW
}

# --- 主函数 ---
function Main {
    param([string[]]$Arguments)
    
    Write-Host "开始Git标签发布流程...`n" -ForegroundColor $COLOR_BLUE

    # --- 1. 参数检查 ---
    Step-Msg "步骤 1/8: 检查命令行参数..."
    if ($Arguments.Count -lt 1) {
        Error-Exit "用法: .\release-tag.ps1 <tag_version> (例如: v1.2.3)"
    }
    $TAG = $Arguments[0]
    Success-Msg "将要创建的标签: $TAG"

    # --- 2. Git环境检查 ---
    Step-Msg "步骤 2/8: 检查Git环境..."
    try {
        $null = git rev-parse --is-inside-work-tree 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Not a git repository"
        }
    }
    catch {
        Error-Exit "当前目录不是一个Git仓库。"
    }
    Success-Msg "当前目录是一个有效的Git仓库。"

    # --- 3. 远程仓库检查 ---
    Step-Msg "步骤 3/8: 检查远程仓库..."
    try {
        $null = git remote get-url $REMOTE_NAME 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Remote not found"
        }
    }
    catch {
        Error-Exit "远程仓库 '$REMOTE_NAME' 不存在。请检查Git配置。"
    }
    Success-Msg "远程仓库 '$REMOTE_NAME' 配置正确。"

    # --- 4. 分支检查 ---
    Step-Msg "步骤 4/8: 检查当前分支..."
    try {
        $current_branch = git rev-parse --abbrev-ref HEAD
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to get current branch"
        }
    }
    catch {
        Error-Exit "无法获取当前分支信息。"
    }
    
    if ($current_branch -ne $MAIN_BRANCH) {
        Error-Exit "必须在 '$MAIN_BRANCH' 分支上执行此脚本。当前分支是 '$current_branch'。"
    }
    Success-Msg "当前位于 '$MAIN_BRANCH' 分支。"

    # --- 5. 工作目录清洁度检查 ---
    Step-Msg "步骤 5/8: 检查工作目录状态..."
    try {
        git diff --quiet 2>$null
        $diff_exit = $LASTEXITCODE
        git diff --cached --quiet 2>$null
        $cached_exit = $LASTEXITCODE
        
        if ($diff_exit -ne 0 -or $cached_exit -ne 0) {
            throw "Working directory not clean"
        }
    }
    catch {
        Error-Exit "工作目录不干净。请提交或储藏您的更改后再试。"
    }
    Success-Msg "工作目录干净。"

    # --- 6. 远程同步检查 ---
    Step-Msg "步骤 6/8: 检查与远程仓库的同步状态..."
    try {
        git fetch $REMOTE_NAME
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to fetch from remote"
        }
    }
    catch {
        Error-Exit "无法从远程仓库获取最新信息。"
    }
    
    try {
        $local_commit = git rev-parse HEAD
        $remote_commit = git rev-parse "$REMOTE_NAME/$MAIN_BRANCH"
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to get commit hashes"
        }
        
        if ($local_commit -ne $remote_commit) {
            throw "Branches not in sync"
        }
    }
    catch {
        Error-Exit "本地 '$MAIN_BRANCH' 分支与远程 '$REMOTE_NAME/$MAIN_BRANCH' 不同步。请先执行 'git pull'。"
    }
    Success-Msg "本地 '$MAIN_BRANCH' 分支已与远程同步。"

    # --- 7. 标签冲突检查 ---
    Step-Msg "步骤 7/8: 检查标签冲突..."
    
    # 检查本地标签
    try {
        $null = git rev-parse -q --verify "refs/tags/$TAG" 2>$null
        if ($LASTEXITCODE -eq 0) {
            throw "Local tag exists"
        }
    }
    catch {
        if ($_.Exception.Message -eq "Local tag exists") {
            Error-Exit "标签 '$TAG' 已在本地存在。"
        }
    }
    
    # 检查远程标签
    try {
        $remote_tags = git ls-remote --tags $REMOTE_NAME 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to list remote tags"
        }
        
        if ($remote_tags -match "refs/tags/$TAG`$") {
            throw "Remote tag exists"
        }
    }
    catch {
        if ($_.Exception.Message -eq "Remote tag exists") {
            Error-Exit "标签 '$TAG' 已在远程仓库 '$REMOTE_NAME' 中存在。"
        }
        elseif ($_.Exception.Message -eq "Failed to list remote tags") {
            Error-Exit "无法获取远程标签列表。"
        }
    }
    Success-Msg "标签 '$TAG' 未发现冲突。"

    # --- 8. 创建并推送标签 ---
    Step-Msg "步骤 8/8: 创建并推送附注标签..."
    $tag_message = "Release version $TAG"

    # 尝试创建标签
    try {
        git tag -a $TAG -m $tag_message
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create tag"
        }
    }
    catch {
        Error-Exit "创建附注标签失败。请检查您的Git配置。"
    }
    Success-Msg "已在本地成功创建标签 '$TAG'。"

    Write-Host "正在将标签推送到远程仓库 '$REMOTE_NAME'..."
    try {
        git push $REMOTE_NAME $TAG
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to push tag"
        }
    }
    catch {
        # 如果推送失败，清理本地创建的标签以保持状态一致
        try {
            git tag -d $TAG >$null 2>&1
        }
        catch {
            # 忽略删除标签时的错误
        }
        Error-Exit "推送标签 '$TAG' 到远程仓库失败。已回滚本地标签创建。"
    }

    Success-Msg "标签 '$TAG' 已成功推送到远程仓库。"

    Write-Host "`n发布流程成功完成！🎉" -ForegroundColor $COLOR_GREEN
}

# 执行主函数
Main $args
